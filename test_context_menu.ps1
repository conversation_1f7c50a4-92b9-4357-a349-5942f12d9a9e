# BandiZip Context Menu Integration Test Script
# This script demonstrates the context menu functionality

Write-Host "=== BandiZip 右键菜单集成测试 ===" -ForegroundColor Green
Write-Host ""

# Check if BandiZip is running
$bandiZipProcess = Get-Process -Name "BandiZip" -ErrorAction SilentlyContinue
if ($bandiZipProcess) {
    Write-Host "✅ BandiZip 正在运行 (PID: $($bandiZipProcess.Id))" -ForegroundColor Green
} else {
    Write-Host "❌ BandiZip 未运行，请先启动应用程序" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 右键菜单功能演示 ===" -ForegroundColor Yellow
Write-Host ""

# Test filename truncation function
Write-Host "📝 文件名截断测试:" -ForegroundColor Cyan

$testFiles = @(
    "短文件名.txt",
    "这是一个中等长度的文件名.zip",
    "这是一个非常非常非常长的文件名超过了24个字符的限制需要被截断处理.rar",
    "VeryLongEnglishFileNameThatExceedsTheLimit.7z",
    "混合中英文VeryLongFileName测试.tar"
)

foreach ($file in $testFiles) {
    $length = 0
    foreach ($char in $file.ToCharArray()) {
        if ([int]$char -gt 127) {
            $length += 2  # 中文字符算2个字符
        } else {
            $length += 1  # 英文字符算1个字符
        }
    }
    
    if ($length -le 24) {
        $truncated = $file
    } else {
        $result = ""
        $currentLength = 0
        foreach ($char in $file.ToCharArray()) {
            $charLength = if ([int]$char -gt 127) { 2 } else { 1 }
            if ($currentLength + $charLength + 3 -gt 24) { break }
            $result += $char
            $currentLength += $charLength
        }
        $truncated = $result + "..."
    }
    
    Write-Host "  原文件名: $file (长度: $length)" -ForegroundColor White
    Write-Host "  菜单显示: $truncated" -ForegroundColor Green
    Write-Host ""
}

Write-Host "=== 右键菜单项预览 ===" -ForegroundColor Yellow
Write-Host ""

Write-Host "📁 压缩文件 (zip/rar/7z/tar/iso) 右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 智能解压到此处" -ForegroundColor Green
Write-Host "      └─ 预览压缩包" -ForegroundColor Green
Write-Host ""

Write-Host "📄 普通文件右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 压缩为'文件名.zip'" -ForegroundColor Green
Write-Host "      ├─ 压缩为'文件名.7z'" -ForegroundColor Green
Write-Host "      └─ 自定义压缩" -ForegroundColor Green
Write-Host ""

Write-Host "📂 文件夹右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 压缩为'文件夹名.zip'" -ForegroundColor Green
Write-Host "      ├─ 压缩为'文件夹名.7z'" -ForegroundColor Green
Write-Host "      └─ 自定义压缩" -ForegroundColor Green
Write-Host ""

Write-Host "=== 注册表集成说明 ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "要启用右键菜单集成，请在 BandiZip 应用程序中点击:" -ForegroundColor White
Write-Host "  1. '注册右键菜单' 按钮 - 注册右键菜单到系统" -ForegroundColor Green
Write-Host "  2. '移除右键菜单' 按钮 - 从系统移除右键菜单" -ForegroundColor Red
Write-Host ""
Write-Host "注意: 需要管理员权限才能修改注册表" -ForegroundColor Yellow
Write-Host ""

Write-Host "=== 演示文件 ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "已创建演示文件:" -ForegroundColor White
Write-Host "  📄 context_menu_demo.reg - 注册表项示例" -ForegroundColor Green
Write-Host "  📄 test_context_menu.ps1 - 本测试脚本" -ForegroundColor Green
Write-Host ""

Write-Host "测试完成! 🎉" -ForegroundColor Green
