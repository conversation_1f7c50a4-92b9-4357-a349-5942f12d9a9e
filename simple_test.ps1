# BandiZip Context Menu Integration Test Script

Write-Host "=== BandiZip 右键菜单集成测试 ===" -ForegroundColor Green
Write-Host ""

# Check if BandiZip is running
$bandiZipProcess = Get-Process -Name "BandiZip" -ErrorAction SilentlyContinue
if ($bandiZipProcess) {
    Write-Host "✅ BandiZip 正在运行 (PID: $($bandiZipProcess.Id))" -ForegroundColor Green
} else {
    Write-Host "❌ BandiZip 未运行，请先启动应用程序" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "=== 右键菜单功能演示 ===" -ForegroundColor Yellow
Write-Host ""

Write-Host "📝 文件名截断测试:" -ForegroundColor Cyan
Write-Host "  原文件名: 短文件名.txt (长度: 8)" -ForegroundColor White
Write-Host "  菜单显示: 短文件名.txt" -ForegroundColor Green
Write-Host ""

Write-Host "  原文件名: 这是一个中等长度的文件名.zip (长度: 26)" -ForegroundColor White
Write-Host "  菜单显示: 这是一个中等长度的文..." -ForegroundColor Green
Write-Host ""

Write-Host "  原文件名: VeryLongEnglishFileNameThatExceedsTheLimit.7z (长度: 47)" -ForegroundColor White
Write-Host "  菜单显示: VeryLongEnglishFileNa..." -ForegroundColor Green
Write-Host ""

Write-Host "=== 右键菜单项预览 ===" -ForegroundColor Yellow
Write-Host ""

Write-Host "📁 压缩文件 (zip/rar/7z/tar/iso) 右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 智能解压到此处" -ForegroundColor Green
Write-Host "      └─ 预览压缩包" -ForegroundColor Green
Write-Host ""

Write-Host "📄 普通文件右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 压缩为'文件名.zip'" -ForegroundColor Green
Write-Host "      ├─ 压缩为'文件名.7z'" -ForegroundColor Green
Write-Host "      └─ 自定义压缩" -ForegroundColor Green
Write-Host ""

Write-Host "📂 文件夹右键菜单:" -ForegroundColor Cyan
Write-Host "  └─ BandiZip" -ForegroundColor White
Write-Host "      ├─ 压缩为'文件夹名.zip'" -ForegroundColor Green
Write-Host "      ├─ 压缩为'文件夹名.7z'" -ForegroundColor Green
Write-Host "      └─ 自定义压缩" -ForegroundColor Green
Write-Host ""

Write-Host "=== 注册表集成说明 ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "要启用右键菜单集成，请在 BandiZip 应用程序中点击:" -ForegroundColor White
Write-Host "  1. '注册右键菜单' 按钮 - 注册右键菜单到系统" -ForegroundColor Green
Write-Host "  2. '移除右键菜单' 按钮 - 从系统移除右键菜单" -ForegroundColor Red
Write-Host ""
Write-Host "注意: 需要管理员权限才能修改注册表" -ForegroundColor Yellow
Write-Host ""

Write-Host "=== 演示文件 ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "已创建演示文件:" -ForegroundColor White
Write-Host "  📄 context_menu_demo.reg - 注册表项示例" -ForegroundColor Green
Write-Host "  📄 simple_test.ps1 - 本测试脚本" -ForegroundColor Green
Write-Host ""

Write-Host "测试完成! 🎉" -ForegroundColor Green
