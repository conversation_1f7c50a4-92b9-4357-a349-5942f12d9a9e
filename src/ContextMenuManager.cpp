#include "ContextMenuManager.h"
#include <QApplication>
#include <QDir>
#include <QDebug>
#include <QStandardPaths>
#include <QFileInfo>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#include <comdef.h>
#endif

ContextMenuManager::ContextMenuManager(QObject *parent)
    : QObject(parent)
{
    // 初始化支持的压缩文件扩展名
    m_archiveExtensions << "zip" << "rar" << "7z" << "tar" << "iso";
    
    // 获取应用程序路径
    m_applicationPath = getApplicationPath();
}

ContextMenuManager::~ContextMenuManager()
{
}

QString ContextMenuManager::getApplicationPath()
{
    return QApplication::applicationFilePath();
}

int ContextMenuManager::getDisplayLength(const QString &text)
{
    int length = 0;
    for (const QChar &ch : text) {
        // 中文字符和中文符号算2个字符，英文和符号算1个字符
        if (ch.unicode() > 127) {
            length += 2;
        } else {
            length += 1;
        }
    }
    return length;
}

QString ContextMenuManager::truncateFileName(const QString &fileName, int maxLength)
{
    if (getDisplayLength(fileName) <= maxLength) {
        return fileName;
    }
    
    // 需要截断，保留前面部分并添加省略号
    QString result;
    int currentLength = 0;
    int ellipsisLength = 3; // "..." 的长度
    
    for (const QChar &ch : fileName) {
        int charLength = (ch.unicode() > 127) ? 2 : 1;
        
        if (currentLength + charLength + ellipsisLength > maxLength) {
            break;
        }
        
        result += ch;
        currentLength += charLength;
    }
    
    result += "...";
    return result;
}

bool ContextMenuManager::registerContextMenu()
{
    qDebug() << "开始注册右键菜单...";
    
    bool success = true;
    
    // 注册压缩文件的右键菜单
    if (!registerArchiveContextMenu()) {
        qWarning() << "注册压缩文件右键菜单失败";
        success = false;
    }
    
    // 注册普通文件的右键菜单
    if (!registerFileContextMenu()) {
        qWarning() << "注册普通文件右键菜单失败";
        success = false;
    }
    
    // 注册文件夹的右键菜单
    if (!registerFolderContextMenu()) {
        qWarning() << "注册文件夹右键菜单失败";
        success = false;
    }
    
    if (success) {
        qDebug() << "右键菜单注册成功";
        emit contextMenuRegistrationChanged(true);
    } else {
        qWarning() << "右键菜单注册失败";
    }
    
    return success;
}

bool ContextMenuManager::unregisterContextMenu()
{
    qDebug() << "开始移除右键菜单...";
    
    bool success = true;
    
    // 移除压缩文件的右键菜单
    if (!unregisterArchiveContextMenu()) {
        qWarning() << "移除压缩文件右键菜单失败";
        success = false;
    }
    
    // 移除普通文件的右键菜单
    if (!unregisterFileContextMenu()) {
        qWarning() << "移除普通文件右键菜单失败";
        success = false;
    }
    
    // 移除文件夹的右键菜单
    if (!unregisterFolderContextMenu()) {
        qWarning() << "移除文件夹右键菜单失败";
        success = false;
    }
    
    if (success) {
        qDebug() << "右键菜单移除成功";
        emit contextMenuRegistrationChanged(false);
    } else {
        qWarning() << "右键菜单移除失败";
    }
    
    return success;
}

bool ContextMenuManager::isContextMenuRegistered()
{
#ifdef _WIN32
    // 检查是否存在BandiZip的注册表项
    return registryKeyExists(HKEY_CLASSES_ROOT, "*/shell/BandiZip");
#else
    return false;
#endif
}

void ContextMenuManager::handleContextMenuCommand(const QString &command, const QStringList &filePaths)
{
    qDebug() << "处理右键菜单命令:" << command << "文件:" << filePaths;
    
    emit contextMenuCommandExecuted(command, filePaths);
    
    // 这里可以添加具体的命令处理逻辑
    // 例如调用压缩或解压功能
}

#ifdef _WIN32
bool ContextMenuManager::createRegistryKey(HKEY hKey, const QString &subKey, 
                                          const QString &valueName, const QString &valueData)
{
    HKEY hSubKey;
    LONG result = RegCreateKeyExW(hKey, 
                                 reinterpret_cast<const wchar_t*>(subKey.utf16()),
                                 0, NULL, REG_OPTION_NON_VOLATILE, 
                                 KEY_WRITE, NULL, &hSubKey, NULL);
    
    if (result != ERROR_SUCCESS) {
        qWarning() << "创建注册表项失败:" << subKey << "错误代码:" << result;
        return false;
    }
    
    if (!valueName.isEmpty() && !valueData.isEmpty()) {
        QByteArray valueDataBytes = valueData.toUtf8();
        result = RegSetValueExW(hSubKey,
                               reinterpret_cast<const wchar_t*>(valueName.utf16()),
                               0, REG_SZ,
                               reinterpret_cast<const BYTE*>(valueDataBytes.constData()),
                               valueDataBytes.size() + 1);
        
        if (result != ERROR_SUCCESS) {
            qWarning() << "设置注册表值失败:" << valueName << "错误代码:" << result;
            RegCloseKey(hSubKey);
            return false;
        }
    }
    
    RegCloseKey(hSubKey);
    return true;
}

bool ContextMenuManager::deleteRegistryKey(HKEY hKey, const QString &subKey)
{
    LONG result = RegDeleteTreeW(hKey, reinterpret_cast<const wchar_t*>(subKey.utf16()));
    
    if (result != ERROR_SUCCESS && result != ERROR_FILE_NOT_FOUND) {
        qWarning() << "删除注册表项失败:" << subKey << "错误代码:" << result;
        return false;
    }
    
    return true;
}

bool ContextMenuManager::registryKeyExists(HKEY hKey, const QString &subKey)
{
    HKEY hSubKey;
    LONG result = RegOpenKeyExW(hKey,
                               reinterpret_cast<const wchar_t*>(subKey.utf16()),
                               0, KEY_READ, &hSubKey);
    
    if (result == ERROR_SUCCESS) {
        RegCloseKey(hSubKey);
        return true;
    }
    
    return false;
}
#endif

bool ContextMenuManager::registerArchiveContextMenu()
{
#ifdef _WIN32
    QString appPath = m_applicationPath;

    // 为每个支持的压缩文件扩展名注册右键菜单
    for (const QString &ext : m_archiveExtensions) {
        QString extKey = QString(".%1").arg(ext);

        // 注册主菜单项 "BandiZip"
        QString shellKey = QString("%1/shell/BandiZip").arg(extKey);
        if (!createRegistryKey(HKEY_CLASSES_ROOT, shellKey, "", "BandiZip")) {
            return false;
        }

        // 设置菜单图标
        QString iconKey = shellKey + "/Icon";
        if (!createRegistryKey(HKEY_CLASSES_ROOT, iconKey, "", appPath + ",0")) {
            return false;
        }

        // 设置子菜单
        QString subCommandsKey = shellKey + "/shell";
        if (!createRegistryKey(HKEY_CLASSES_ROOT, subCommandsKey, "", "")) {
            return false;
        }

        // 智能解压到此处
        QString extractHereKey = subCommandsKey + "/ExtractHere";
        if (!createRegistryKey(HKEY_CLASSES_ROOT, extractHereKey, "", "智能解压到此处")) {
            return false;
        }

        QString extractHereCommandKey = extractHereKey + "/command";
        QString extractCommand = QString("\"%1\" -extract \"%2\"").arg(appPath).arg("%1");
        if (!createRegistryKey(HKEY_CLASSES_ROOT, extractHereCommandKey, "", extractCommand)) {
            return false;
        }

        // 预览压缩包
        QString previewKey = subCommandsKey + "/Preview";
        if (!createRegistryKey(HKEY_CLASSES_ROOT, previewKey, "", "预览压缩包")) {
            return false;
        }

        QString previewCommandKey = previewKey + "/command";
        QString previewCommand = QString("\"%1\" -preview \"%2\"").arg(appPath).arg("%1");
        if (!createRegistryKey(HKEY_CLASSES_ROOT, previewCommandKey, "", previewCommand)) {
            return false;
        }
    }

    return true;
#else
    return false;
#endif
}

bool ContextMenuManager::registerFileContextMenu()
{
#ifdef _WIN32
    QString appPath = m_applicationPath;

    // 注册所有文件的右键菜单 (*)
    QString shellKey = "*/shell/BandiZip";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, shellKey, "", "BandiZip")) {
        return false;
    }

    // 设置菜单图标
    QString iconKey = shellKey + "/Icon";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, iconKey, "", appPath + ",0")) {
        return false;
    }

    // 设置子菜单
    QString subCommandsKey = shellKey + "/shell";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, subCommandsKey, "", "")) {
        return false;
    }

    // 压缩为ZIP
    QString compressZipKey = subCommandsKey + "/CompressZip";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compressZipKey, "", "压缩为\"%1.zip\"")) {
        return false;
    }

    QString compressZipCommandKey = compressZipKey + "/command";
    QString zipCommand = QString("\"%1\" -compress-zip \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compressZipCommandKey, "", zipCommand)) {
        return false;
    }

    // 压缩为7Z
    QString compress7zKey = subCommandsKey + "/Compress7z";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compress7zKey, "", "压缩为\"%1.7z\"")) {
        return false;
    }

    QString compress7zCommandKey = compress7zKey + "/command";
    QString sevenZCommand = QString("\"%1\" -compress-7z \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compress7zCommandKey, "", sevenZCommand)) {
        return false;
    }

    // 自定义压缩
    QString customCompressKey = subCommandsKey + "/CustomCompress";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, customCompressKey, "", "自定义压缩")) {
        return false;
    }

    QString customCompressCommandKey = customCompressKey + "/command";
    QString customCommand = QString("\"%1\" -compress-custom \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, customCompressCommandKey, "", customCommand)) {
        return false;
    }

    return true;
#else
    return false;
#endif
}

bool ContextMenuManager::registerFolderContextMenu()
{
#ifdef _WIN32
    QString appPath = m_applicationPath;

    // 注册文件夹的右键菜单
    QString shellKey = "Directory/shell/BandiZip";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, shellKey, "", "BandiZip")) {
        return false;
    }

    // 设置菜单图标
    QString iconKey = shellKey + "/Icon";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, iconKey, "", appPath + ",0")) {
        return false;
    }

    // 设置子菜单
    QString subCommandsKey = shellKey + "/shell";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, subCommandsKey, "", "")) {
        return false;
    }

    // 压缩为ZIP
    QString compressZipKey = subCommandsKey + "/CompressZip";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compressZipKey, "", "压缩为\"%1.zip\"")) {
        return false;
    }

    QString compressZipCommandKey = compressZipKey + "/command";
    QString zipCommand = QString("\"%1\" -compress-zip \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compressZipCommandKey, "", zipCommand)) {
        return false;
    }

    // 压缩为7Z
    QString compress7zKey = subCommandsKey + "/Compress7z";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compress7zKey, "", "压缩为\"%1.7z\"")) {
        return false;
    }

    QString compress7zCommandKey = compress7zKey + "/command";
    QString sevenZCommand = QString("\"%1\" -compress-7z \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, compress7zCommandKey, "", sevenZCommand)) {
        return false;
    }

    // 自定义压缩
    QString customCompressKey = subCommandsKey + "/CustomCompress";
    if (!createRegistryKey(HKEY_CLASSES_ROOT, customCompressKey, "", "自定义压缩")) {
        return false;
    }

    QString customCompressCommandKey = customCompressKey + "/command";
    QString customCommand = QString("\"%1\" -compress-custom \"%2\"").arg(appPath).arg("%1");
    if (!createRegistryKey(HKEY_CLASSES_ROOT, customCompressCommandKey, "", customCommand)) {
        return false;
    }

    return true;
#else
    return false;
#endif
}

bool ContextMenuManager::unregisterArchiveContextMenu()
{
#ifdef _WIN32
    // 为每个支持的压缩文件扩展名移除右键菜单
    for (const QString &ext : m_archiveExtensions) {
        QString extKey = QString(".%1/shell/BandiZip").arg(ext);
        if (!deleteRegistryKey(HKEY_CLASSES_ROOT, extKey)) {
            return false;
        }
    }
    return true;
#else
    return false;
#endif
}

bool ContextMenuManager::unregisterFileContextMenu()
{
#ifdef _WIN32
    return deleteRegistryKey(HKEY_CLASSES_ROOT, "*/shell/BandiZip");
#else
    return false;
#endif
}

bool ContextMenuManager::unregisterFolderContextMenu()
{
#ifdef _WIN32
    return deleteRegistryKey(HKEY_CLASSES_ROOT, "Directory/shell/BandiZip");
#else
    return false;
#endif
}
