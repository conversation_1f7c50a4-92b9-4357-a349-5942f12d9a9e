#include "MainWindow.h"
#include "CompressionEngine.h"
#include "FeatureCard.h"
#include <QApplication>
#include <QScreen>
#include <QSplashScreen>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_titleBar(nullptr)
    , m_contentArea(nullptr)
    , m_scrollArea(nullptr)
    , m_title<PERSON>abel(nullptr)
    , m_minimizeBtn(nullptr)
    , m_maximizeBtn(nullptr)
    , m_closeBtn(nullptr)
    , m_statusLabel(nullptr)
    , m_extractCard(nullptr)
    , m_compressCard(nullptr)
    , m_progressDialog(nullptr)
    , m_compressionEngine(nullptr)
    , m_dragging(false)
{
    setupUI();
    setupStyles();

    // 初始化压缩引擎
    m_compressionEngine = new CompressionEngine(this);
    connect(m_compressionEngine, &CompressionEngine::progressChanged,
            this, &MainWindow::onCompressionProgress);
    connect(m_compressionEngine, &CompressionEngine::finished,
            this, &MainWindow::onCompressionFinished);

    // 启用拖放
    setAcceptDrops(true);

    // 设置窗口属性
    setWindowTitle("好压万能压缩");
    setWindowFlags(Qt::FramelessWindowHint);
    setMinimumSize(900, 650);
    resize(1000, 700);

    // 居中显示
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
}

MainWindow::~MainWindow()
{
    if (m_compressionEngine) {
        delete m_compressionEngine;
    }
}

void MainWindow::setupUI()
{
    // 创建中央部件
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);

    // 创建主布局
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);

    // 设置各个区域
    setupTitleBar();
    setupMainContent();
    setupStatusBar();
}

void MainWindow::setupTitleBar()
{
    // 创建标题栏
    m_titleBar = new QWidget();
    m_titleBar->setFixedHeight(50);
    m_titleBar->setObjectName("titleBar");

    QHBoxLayout *titleLayout = new QHBoxLayout(m_titleBar);
    titleLayout->setContentsMargins(15, 0, 10, 0);
    titleLayout->setSpacing(10);

    // 应用图标和标题
    QLabel *iconLabel = new QLabel();
    iconLabel->setFixedSize(24, 24);
    iconLabel->setStyleSheet("background-color: #4A90E2; border-radius: 4px;");
    iconLabel->setText("📦");
    iconLabel->setAlignment(Qt::AlignCenter);

    m_titleLabel = new QLabel("好压万能压缩");
    m_titleLabel->setStyleSheet("color: #333; font-size: 14px; font-weight: bold;");

    titleLayout->addWidget(iconLabel);
    titleLayout->addWidget(m_titleLabel);
    titleLayout->addStretch();

    // 右侧按钮区域
    QWidget *buttonArea = new QWidget();
    QHBoxLayout *buttonLayout = new QHBoxLayout(buttonArea);
    buttonLayout->setContentsMargins(0, 0, 0, 0);
    buttonLayout->setSpacing(5);

    // 立即登录按钮
    QPushButton *loginBtn = new QPushButton("立即登录");
    loginBtn->setObjectName("loginButton");

    // 立即开通按钮
    QPushButton *upgradeBtn = new QPushButton("立即开通");
    upgradeBtn->setObjectName("upgradeButton");

    // 窗口控制按钮
    m_minimizeBtn = new QPushButton("−");
    m_maximizeBtn = new QPushButton("□");
    m_closeBtn = new QPushButton("×");

    m_minimizeBtn->setObjectName("windowButton");
    m_maximizeBtn->setObjectName("windowButton");
    m_closeBtn->setObjectName("closeButton");

    connect(m_minimizeBtn, &QPushButton::clicked, this, &MainWindow::onMinimizeClicked);
    connect(m_maximizeBtn, &QPushButton::clicked, this, &MainWindow::onMaximizeClicked);
    connect(m_closeBtn, &QPushButton::clicked, this, &MainWindow::onCloseClicked);

    buttonLayout->addWidget(loginBtn);
    buttonLayout->addWidget(upgradeBtn);
    buttonLayout->addWidget(m_minimizeBtn);
    buttonLayout->addWidget(m_maximizeBtn);
    buttonLayout->addWidget(m_closeBtn);

    titleLayout->addWidget(buttonArea);
    m_mainLayout->addWidget(m_titleBar);
}

void MainWindow::setupMainContent()
{
    // 创建滚动区域
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setObjectName("scrollArea");

    // 创建内容区域
    m_contentArea = new QWidget();
    m_contentArea->setObjectName("contentArea");

    QVBoxLayout *contentLayout = new QVBoxLayout(m_contentArea);
    contentLayout->setContentsMargins(30, 30, 30, 30);
    contentLayout->setSpacing(20);

    // 创建功能卡片网格
    QGridLayout *cardGrid = new QGridLayout();
    cardGrid->setSpacing(20);
    cardGrid->setContentsMargins(0, 0, 0, 0);

    // 创建功能卡片 - 使用icons目录下的图标
    m_extractCard = createFeatureCard("解压", "点击/拖入压缩包，一键快速解压",
                                     ":/icons/extract.png", "#00D4AA");
    m_compressCard = createFeatureCard("压缩", "点击/拖入文件，支持快速压缩为Zip、7z",
                                      ":/icons/compress.png", "#4A90E2");

    // 连接信号
    connect(m_extractCard, &FeatureCard::clicked, this, &MainWindow::onExtractClicked);
    connect(m_compressCard, &FeatureCard::clicked, this, &MainWindow::onCompressClicked);

    // 添加卡片到网格 (1行2列布局)
    cardGrid->addWidget(m_extractCard, 0, 0);
    cardGrid->addWidget(m_compressCard, 0, 1);

    // 设置列拉伸
    cardGrid->setColumnStretch(0, 1);
    cardGrid->setColumnStretch(1, 1);

    contentLayout->addLayout(cardGrid);
    contentLayout->addStretch();

    m_scrollArea->setWidget(m_contentArea);
    m_mainLayout->addWidget(m_scrollArea);
}

FeatureCard* MainWindow::createFeatureCard(const QString &title, const QString &subtitle,
                                          const QString &iconPath, const QString &color)
{
    return new FeatureCard(title, subtitle, iconPath, color, this);
}

void MainWindow::setupStyles()
{
    // 设置整体样式表
    setStyleSheet(R"(
        QMainWindow {
            background-color: #f8f9fa;
        }

        #titleBar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
            border-bottom: 1px solid #e0e0e0;
        }

        #loginButton {
            background-color: #e3f2fd;
            border: 1px solid #90caf9;
            border-radius: 4px;
            padding: 6px 12px;
            color: #1976d2;
            font-size: 12px;
        }

        #loginButton:hover {
            background-color: #bbdefb;
        }

        #upgradeButton {
            background-color: #ff9800;
            border: 1px solid #f57c00;
            border-radius: 4px;
            padding: 6px 12px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        #upgradeButton:hover {
            background-color: #f57c00;
        }

        #windowButton {
            background-color: transparent;
            border: none;
            color: #666;
            font-size: 16px;
            font-weight: bold;
            padding: 8px 12px;
            min-width: 30px;
        }

        #windowButton:hover {
            background-color: #e0e0e0;
        }

        #closeButton {
            background-color: transparent;
            border: none;
            color: #666;
            font-size: 16px;
            font-weight: bold;
            padding: 8px 12px;
            min-width: 30px;
        }

        #closeButton:hover {
            background-color: #f44336;
            color: white;
        }

        #scrollArea {
            border: none;
            background-color: transparent;
        }

        #contentArea {
            background-color: #f8f9fa;
        }

        QScrollBar:vertical {
            background-color: #f0f0f0;
            width: 8px;
            border-radius: 4px;
        }

        QScrollBar::handle:vertical {
            background-color: #c0c0c0;
            border-radius: 4px;
            min-height: 20px;
        }

        QScrollBar::handle:vertical:hover {
            background-color: #a0a0a0;
        }
    )");
}

void MainWindow::setupStatusBar()
{
    // 创建状态标签
    m_statusLabel = new QLabel("就绪");
    m_statusLabel->setStyleSheet("color: #666; font-size: 12px; padding: 5px;");

    // 添加到状态栏
    statusBar()->addWidget(m_statusLabel);
    statusBar()->showMessage("欢迎使用好压万能压缩");

    // 设置状态栏样式
    statusBar()->setStyleSheet(
        "QStatusBar {"
        "   background-color: #f0f0f0;"
        "   border-top: 1px solid #e0e0e0;"
        "   color: #666;"
        "}"
    );
}

void MainWindow::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    }
}

void MainWindow::dropEvent(QDropEvent *event)
{
    const QMimeData *mimeData = event->mimeData();
    if (mimeData->hasUrls()) {
        QList<QUrl> urls = mimeData->urls();
        if (!urls.isEmpty()) {
            QString filePath = urls.first().toLocalFile();
            processFile(filePath);
        }
    }
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
}

void MainWindow::onExtractClicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "打开压缩文件", "",
        "压缩文件 (*.zip *.rar *.7z *.tar *.iso);;所有文件 (*.*)");

    if (!fileName.isEmpty()) {
        processFile(fileName);
    }
}

void MainWindow::onCompressClicked()
{
    QStringList fileNames = QFileDialog::getOpenFileNames(this,
        "选择要压缩的文件", "",
        "所有文件 (*.*)");

    if (!fileNames.isEmpty()) {
        QString archiveName = QFileDialog::getSaveFileName(this,
            "保存压缩文件", "",
            "ZIP 文件 (*.zip);;7Z 文件 (*.7z)");

        if (!archiveName.isEmpty()) {
            // 显示进度对话框
            m_progressDialog = new QProgressDialog("正在压缩文件...", "取消", 0, 100, this);
            m_progressDialog->setWindowModality(Qt::WindowModal);
            m_progressDialog->show();

            // 开始压缩
            m_compressionEngine->compressFiles(fileNames, archiveName);
        }
    }
}



void MainWindow::onMinimizeClicked()
{
    showMinimized();
}

void MainWindow::onMaximizeClicked()
{
    if (isMaximized()) {
        showNormal();
    } else {
        showMaximized();
    }
}

void MainWindow::onCloseClicked()
{
    close();
}

void MainWindow::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        // 检查是否点击在标题栏区域
        if (m_titleBar && m_titleBar->geometry().contains(event->pos())) {
            m_dragging = true;
            m_dragPosition = event->globalPosition().toPoint() - frameGeometry().topLeft();
            event->accept();
            return;
        }
    }
    QMainWindow::mousePressEvent(event);
}

void MainWindow::mouseMoveEvent(QMouseEvent *event)
{
    if (event->buttons() & Qt::LeftButton && m_dragging) {
        move(event->globalPosition().toPoint() - m_dragPosition);
        event->accept();
        return;
    }
    QMainWindow::mouseMoveEvent(event);
}

void MainWindow::mouseReleaseEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        m_dragging = false;
        event->accept();
        return;
    }
    QMainWindow::mouseReleaseEvent(event);
}

void MainWindow::onCompressionProgress(int percentage)
{
    if (m_progressDialog) {
        m_progressDialog->setValue(percentage);
    }
    m_statusLabel->setText(QString("进度: %1%").arg(percentage));
}

void MainWindow::onCompressionFinished(bool success, const QString &message)
{
    if (m_progressDialog) {
        m_progressDialog->close();
        delete m_progressDialog;
        m_progressDialog = nullptr;
    }

    if (success) {
        // 成功时只更新状态，不显示弹框
        m_statusLabel->setText("操作完成 - " + message);
        qDebug() << "✅ Operation completed successfully:" << message;
    } else {
        // 失败时显示错误信息
        QMessageBox::critical(this, "错误", message);
        m_statusLabel->setText("操作失败");
        qDebug() << "❌ Operation failed:" << message;
    }
}

void MainWindow::processFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();

    // 检查是否为支持的压缩格式
    QStringList supportedFormats = {"zip", "rar", "7z", "tar", "iso"};

    if (supportedFormats.contains(suffix)) {
        // 选择解压目录
        QString extractDir = QFileDialog::getExistingDirectory(this,
            "选择解压目录", fileInfo.absolutePath());

        if (!extractDir.isEmpty()) {
            // 显示进度对话框
            m_progressDialog = new QProgressDialog("正在解压文件...", "取消", 0, 100, this);
            m_progressDialog->setWindowModality(Qt::WindowModal);
            m_progressDialog->show();

            // 开始解压
            m_compressionEngine->extractArchive(filePath, extractDir);
        }
    } else {
        QMessageBox::warning(this, "不支持的格式",
            QString("文件格式 '%1' 不受支持。\n支持的格式: ZIP, RAR, 7Z, TAR, ISO").arg(suffix.toUpper()));
    }
}
