#ifndef CONTEXTMENUMANAGER_H
#define CONTEXTMENUMANAGER_H

#include <QString>
#include <QStringList>
#include <QObject>

#ifdef _WIN32
#include <windows.h>
#include <shlobj.h>
#include <comdef.h>
#endif

/**
 * @brief Windows右键菜单管理器
 * 
 * 负责注册和管理BandiZip的Windows右键菜单集成
 * 支持压缩文件的解压菜单和普通文件的压缩菜单
 */
class ContextMenuManager : public QObject
{
    Q_OBJECT

public:
    explicit ContextMenuManager(QObject *parent = nullptr);
    ~ContextMenuManager();

    /**
     * @brief 注册右键菜单到Windows注册表
     * @return 成功返回true，失败返回false
     */
    bool registerContextMenu();

    /**
     * @brief 从Windows注册表移除右键菜单
     * @return 成功返回true，失败返回false
     */
    bool unregisterContextMenu();

    /**
     * @brief 检查右键菜单是否已注册
     * @return 已注册返回true，未注册返回false
     */
    bool isContextMenuRegistered();

    /**
     * @brief 处理右键菜单命令
     * @param command 命令字符串
     * @param filePaths 选中的文件路径列表
     */
    void handleContextMenuCommand(const QString &command, const QStringList &filePaths);

    /**
     * @brief 获取应用程序路径
     * @return 应用程序完整路径
     */
    static QString getApplicationPath();

    /**
     * @brief 截断文件名用于菜单显示
     * @param fileName 原始文件名
     * @param maxLength 最大长度（默认24个字符）
     * @return 截断后的文件名
     */
    static QString truncateFileName(const QString &fileName, int maxLength = 24);

    /**
     * @brief 计算字符串的显示长度（中文字符算2个长度）
     * @param text 要计算的字符串
     * @return 显示长度
     */
    static int getDisplayLength(const QString &text);

private:
    /**
     * @brief 注册压缩文件的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool registerArchiveContextMenu();

    /**
     * @brief 注册普通文件的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool registerFileContextMenu();

    /**
     * @brief 注册文件夹的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool registerFolderContextMenu();

    /**
     * @brief 移除压缩文件的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool unregisterArchiveContextMenu();

    /**
     * @brief 移除普通文件的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool unregisterFileContextMenu();

    /**
     * @brief 移除文件夹的右键菜单
     * @return 成功返回true，失败返回false
     */
    bool unregisterFolderContextMenu();

#ifdef _WIN32
    /**
     * @brief 创建注册表项
     * @param hKey 父键
     * @param subKey 子键路径
     * @param valueName 值名称
     * @param valueData 值数据
     * @return 成功返回true，失败返回false
     */
    bool createRegistryKey(HKEY hKey, const QString &subKey, 
                          const QString &valueName, const QString &valueData);

    /**
     * @brief 删除注册表项
     * @param hKey 父键
     * @param subKey 子键路径
     * @return 成功返回true，失败返回false
     */
    bool deleteRegistryKey(HKEY hKey, const QString &subKey);

    /**
     * @brief 检查注册表项是否存在
     * @param hKey 父键
     * @param subKey 子键路径
     * @return 存在返回true，不存在返回false
     */
    bool registryKeyExists(HKEY hKey, const QString &subKey);
#endif

    // 支持的压缩文件扩展名
    QStringList m_archiveExtensions;
    
    // 应用程序路径
    QString m_applicationPath;

signals:
    /**
     * @brief 右键菜单注册状态改变信号
     * @param registered 是否已注册
     */
    void contextMenuRegistrationChanged(bool registered);

    /**
     * @brief 右键菜单命令执行信号
     * @param command 执行的命令
     * @param filePaths 相关文件路径
     */
    void contextMenuCommandExecuted(const QString &command, const QStringList &filePaths);
};

#endif // CONTEXTMENUMANAGER_H
