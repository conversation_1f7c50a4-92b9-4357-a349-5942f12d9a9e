#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QToolBar>
#include <QAction>
#include <QLabel>
#include <QPushButton>
#include <QFrame>
#include <QStatusBar>
#include <QFileDialog>
#include <QMessageBox>
#include <QProgressDialog>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QWidget>
#include <QScrollArea>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

class CompressionEngine;
class FeatureCard;
class ContextMenuManager;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;

private slots:
    void onExtractClicked();
    void onCompressClicked();
    void onCompressionProgress(int percentage);
    void onCompressionFinished(bool success, const QString &message);
    void onMinimizeClicked();
    void onMaximizeClicked();
    void onCloseClicked();
    void onRegisterContextMenu();
    void onUnregisterContextMenu();

private:
    void setupUI();
    void setupTitleBar();
    void setupMainContent();
    void setupStatusBar();
    void setupStyles();
    void processFile(const QString &filePath);
    FeatureCard* createFeatureCard(const QString &title, const QString &subtitle,
                                  const QString &iconPath, const QString &color);

    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QWidget *m_titleBar;
    QWidget *m_contentArea;
    QScrollArea *m_scrollArea;

    // 标题栏组件
    QLabel *m_titleLabel;
    QPushButton *m_minimizeBtn;
    QPushButton *m_maximizeBtn;
    QPushButton *m_closeBtn;

    // 状态栏组件
    QLabel *m_statusLabel;

    // 功能卡片
    FeatureCard *m_extractCard;
    FeatureCard *m_compressCard;

    // 状态和进度
    QProgressDialog *m_progressDialog;

    // 业务逻辑
    CompressionEngine *m_compressionEngine;
    ContextMenuManager *m_contextMenuManager;

    // 窗口拖动相关
    bool m_dragging;
    QPoint m_dragPosition;
};

#endif // MAINWINDOW_H
