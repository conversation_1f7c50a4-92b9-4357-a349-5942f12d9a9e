cmake_minimum_required(VERSION 3.16)
project(BandiZip VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Find Qt6 components
find_package(Qt6 COMPONENTS
        Core
        Gui
        Widgets
        REQUIRED)

# Set third-party library paths
set(THIRD_PARTY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/third_party")
set(BIT7Z_DIR "${THIRD_PARTY_DIR}/bit7z")
set(SEVENZIP_DIR "${THIRD_PARTY_DIR}/7zip")

# Check for project-local bit7z source code
set(BIT7Z_FOUND FALSE)
if(EXISTS "${BIT7Z_DIR}/include/bit7z/bit7zlibrary.hpp" AND EXISTS "${BIT7Z_DIR}/src")
    set(BIT7Z_FOUND TRUE)
    set(BIT7Z_INCLUDE_DIRS "${BIT7Z_DIR}/include")
    
    # Collect bit7z source files
    file(GLOB BIT7Z_SOURCES "${BIT7Z_DIR}/src/*.cpp")
    file(GLOB BIT7Z_INTERNAL_SOURCES "${BIT7Z_DIR}/src/internal/*.cpp")
    list(APPEND BIT7Z_SOURCES ${BIT7Z_INTERNAL_SOURCES})
    
    list(LENGTH BIT7Z_SOURCES BIT7Z_SOURCE_COUNT)
    message(STATUS "Found project-local bit7z source code with ${BIT7Z_SOURCE_COUNT} files")
else()
    # Fallback to system-installed bit7z
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(BIT7Z QUIET bit7z)
    endif()
    
    if(NOT BIT7Z_FOUND)
        find_path(BIT7Z_INCLUDE_DIR
            NAMES bit7z/bit7z.hpp bit7z.hpp
            PATHS
                ${CMAKE_PREFIX_PATH}/include
                ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/include
                C:/vcpkg/installed/x64-windows/include
                C:/bit7z/include
            PATH_SUFFIXES bit7z
        )
        
        find_library(BIT7Z_LIBRARY
            NAMES bit7z bit7z64
            PATHS
                ${CMAKE_PREFIX_PATH}/lib
                ${VCPKG_INSTALLED_DIR}/${VCPKG_TARGET_TRIPLET}/lib
                C:/vcpkg/installed/x64-windows/lib
                C:/bit7z/lib
        )
        
        if(BIT7Z_INCLUDE_DIR AND BIT7Z_LIBRARY)
            set(BIT7Z_FOUND TRUE)
            set(BIT7Z_INCLUDE_DIRS ${BIT7Z_INCLUDE_DIR})
            set(BIT7Z_LIBRARIES ${BIT7Z_LIBRARY})
            message(STATUS "Found system bit7z: ${BIT7Z_LIBRARIES}")
        endif()
    endif()
endif()

# Add source files
set(SOURCES
    main.cpp
    src/MainWindow.cpp
    src/CompressionEngine.cpp
    src/FeatureCard.cpp
    src/ContextMenuManager.cpp
)

# Add bit7z source files if available
if(BIT7Z_FOUND AND BIT7Z_SOURCES)
    list(APPEND SOURCES ${BIT7Z_SOURCES})
    message(STATUS "Added ${BIT7Z_SOURCE_COUNT} bit7z source files to build")
endif()

set(HEADERS
    src/MainWindow.h
    src/CompressionEngine.h
    src/FeatureCard.h
    src/ContextMenuManager.h
)

# Add resource files
set(RESOURCES
    resources/BandiZip.qrc
)

add_executable(BandiZip ${SOURCES} ${HEADERS} ${RESOURCES})

# Configure bit7z
if(BIT7Z_FOUND)
    target_include_directories(BandiZip PRIVATE
        ${BIT7Z_INCLUDE_DIRS}
        ${BIT7Z_DIR}/include/bit7z
        ${BIT7Z_DIR}/src
        ${SEVENZIP_DIR}/CPP
    )
    target_link_libraries(BandiZip
            Qt6::Core
            Qt6::Gui
            Qt6::Widgets
    )
    target_compile_definitions(BandiZip PRIVATE
        HAVE_BIT7Z
        SEVENZIP_2301  # Define 7-Zip version to fix override issues
    )
    if(BIT7Z_SOURCES)
        message(STATUS "bit7z integration enabled (compiled from source)")
    else()
        message(STATUS "bit7z integration enabled (using system library)")
    endif()
else()
    target_link_libraries(BandiZip
            Qt6::Core
            Qt6::Gui
            Qt6::Widgets
    )
    message(WARNING "bit7z not found, using fallback 7z.exe method")
endif()

# Windows deployment configuration
if (WIN32)
    set(DEBUG_SUFFIX)
    if (MSVC AND CMAKE_BUILD_TYPE MATCHES "Debug")
        set(DEBUG_SUFFIX "d")
    endif ()
    
    set(QT_INSTALL_PATH "${CMAKE_PREFIX_PATH}")
    if (NOT EXISTS "${QT_INSTALL_PATH}/bin")
        set(QT_INSTALL_PATH "${QT_INSTALL_PATH}/..")
        if (NOT EXISTS "${QT_INSTALL_PATH}/bin")
            set(QT_INSTALL_PATH "${QT_INSTALL_PATH}/..")
        endif ()
    endif ()
    
    # Copy Qt6 platform plugins
    if (EXISTS "${QT_INSTALL_PATH}/plugins/platforms/qwindows${DEBUG_SUFFIX}.dll")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E make_directory
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>/plugins/platforms/")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${QT_INSTALL_PATH}/plugins/platforms/qwindows${DEBUG_SUFFIX}.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>/plugins/platforms/")
    endif ()
    
    # Copy Qt6 DLL files
    foreach (QT_LIB Core Gui Widgets)
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${QT_INSTALL_PATH}/bin/Qt6${QT_LIB}${DEBUG_SUFFIX}.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
    endforeach (QT_LIB)
    
    # Copy project-local 7z.dll
    if(EXISTS "${SEVENZIP_DIR}/7z.dll")
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                COMMAND ${CMAKE_COMMAND} -E copy
                "${SEVENZIP_DIR}/7z.dll"
                "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
        message(STATUS "Will copy project-local 7z.dll")
    else()
        # Fallback to system 7z.dll
        set(SYSTEM_SEVENZIP_PATHS
            "C:/Program Files/7-Zip/7z.dll"
            "C:/Program Files (x86)/7-Zip/7z.dll"
        )
        foreach(SEVENZIP_PATH ${SYSTEM_SEVENZIP_PATHS})
            if(EXISTS ${SEVENZIP_PATH})
                add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
                        COMMAND ${CMAKE_COMMAND} -E copy
                        "${SEVENZIP_PATH}"
                        "$<TARGET_FILE_DIR:${PROJECT_NAME}>")
                message(STATUS "Will copy system 7z.dll: ${SEVENZIP_PATH}")
                break()
            endif()
        endforeach()
    endif()
endif ()

# Set output directory
set_target_properties(BandiZip PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}"
    RUNTIME_OUTPUT_DIRECTORY_DEBUG "${CMAKE_BINARY_DIR}"
    RUNTIME_OUTPUT_DIRECTORY_RELEASE "${CMAKE_BINARY_DIR}"
)

# Install configuration
install(TARGETS BandiZip
    RUNTIME DESTINATION bin
)

# Install dependencies
if(WIN32)
    if(EXISTS "${SEVENZIP_DIR}/7z.dll")
        install(FILES "${SEVENZIP_DIR}/7z.dll" DESTINATION bin)
    endif()
endif()
