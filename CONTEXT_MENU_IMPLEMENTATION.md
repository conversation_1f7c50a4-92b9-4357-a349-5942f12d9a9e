# BandiZip Windows 右键菜单集成实现

## 概述

本文档描述了为 BandiZip 压缩软件实现的 Windows 右键菜单集成功能。该功能允许用户通过右键点击文件或文件夹来快速访问压缩和解压功能。

## 功能特性

### 1. 压缩文件右键菜单 (zip/rar/7z/tar/iso)

当用户右键点击支持的压缩文件时，会显示以下菜单结构：

```
BandiZip
├─ 智能解压到此处
└─ 预览压缩包
```

### 2. 普通文件右键菜单

当用户右键点击普通文件时，会显示以下菜单结构：

```
BandiZip
├─ 压缩为"文件名.zip"
├─ 压缩为"文件名.7z"
└─ 自定义压缩
```

### 3. 文件夹右键菜单

当用户右键点击文件夹时，会显示以下菜单结构：

```
BandiZip
├─ 压缩为"文件夹名.zip"
├─ 压缩为"文件夹名.7z"
└─ 自定义压缩
```

## 文件名截断规则

为了确保菜单项的美观性，实现了智能文件名截断功能：

- **字符长度计算**：中文字符和中文符号算 2 个字符，英文和符号算 1 个字符
- **截断规则**：
  - 当文件名显示长度 ≤ 24 个字符时：显示完整文件名
  - 当文件名显示长度 > 24 个字符时：截断并添加省略号 "..."

### 示例

| 原文件名 | 显示长度 | 菜单显示 |
|---------|---------|----------|
| 短文件名.txt | 8 | 短文件名.txt |
| 这是一个中等长度的文件名.zip | 26 | 这是一个中等长度的文... |
| VeryLongEnglishFileNameThatExceedsTheLimit.7z | 47 | VeryLongEnglishFileNa... |

## 技术实现

### 核心类：ContextMenuManager

#### 主要方法

1. **registerContextMenu()** - 注册右键菜单到 Windows 注册表
2. **unregisterContextMenu()** - 从 Windows 注册表移除右键菜单
3. **isContextMenuRegistered()** - 检查右键菜单是否已注册
4. **handleContextMenuCommand()** - 处理右键菜单命令
5. **truncateFileName()** - 截断文件名用于菜单显示
6. **getDisplayLength()** - 计算字符串的显示长度

#### 注册表结构

右键菜单通过以下注册表项实现：

```
HKEY_CLASSES_ROOT
├─ .zip\shell\BandiZip\
├─ .rar\shell\BandiZip\
├─ .7z\shell\BandiZip\
├─ .tar\shell\BandiZip\
├─ .iso\shell\BandiZip\
├─ *\shell\BandiZip\
└─ Directory\shell\BandiZip\
```

每个菜单项包含：
- 显示文本
- 图标路径
- 命令行参数

### UI 集成

在 MainWindow 中添加了两个按钮：
- **注册右键菜单** - 调用 `registerContextMenu()`
- **移除右键菜单** - 调用 `unregisterContextMenu()`

## 文件结构

### 新增文件

1. **src/ContextMenuManager.h** - 右键菜单管理器头文件
2. **src/ContextMenuManager.cpp** - 右键菜单管理器实现
3. **context_menu_demo.reg** - 注册表项示例文件
4. **simple_test.ps1** - 功能测试脚本

### 修改文件

1. **src/MainWindow.h** - 添加 ContextMenuManager 声明和方法
2. **src/MainWindow.cpp** - 集成右键菜单管理功能
3. **CMakeLists.txt** - 添加新源文件到构建系统

## 使用方法

### 1. 注册右键菜单

1. 以管理员身份运行 BandiZip
2. 点击标题栏中的"注册右键菜单"按钮
3. 系统会将菜单项添加到 Windows 注册表
4. 成功后会显示确认消息

### 2. 使用右键菜单

注册成功后，用户可以：
1. 右键点击压缩文件 → 选择"智能解压到此处"或"预览压缩包"
2. 右键点击普通文件 → 选择压缩选项
3. 右键点击文件夹 → 选择压缩选项

### 3. 移除右键菜单

1. 以管理员身份运行 BandiZip
2. 点击标题栏中的"移除右键菜单"按钮
3. 系统会从 Windows 注册表移除菜单项

## 权限要求

- **管理员权限**：修改 HKEY_CLASSES_ROOT 需要管理员权限
- **注册表访问**：需要读写注册表的权限

## 兼容性

- **操作系统**：Windows 7 及以上版本
- **架构**：x64 和 x86
- **Qt 版本**：Qt6.x

## 测试

运行测试脚本验证功能：

```powershell
powershell -ExecutionPolicy Bypass -File simple_test.ps1
```

测试脚本会检查：
1. BandiZip 是否正在运行
2. 文件名截断功能演示
3. 右键菜单结构预览
4. 使用说明

## 注意事项

1. **安全性**：只有管理员可以注册/移除右键菜单
2. **备份**：建议在修改注册表前备份相关项
3. **卸载**：卸载软件时应自动移除右键菜单项
4. **多语言**：当前实现为中文，可扩展支持多语言

## 未来改进

1. **多语言支持**：根据系统语言显示相应菜单文本
2. **图标优化**：为不同菜单项使用不同图标
3. **动态菜单**：根据文件类型动态生成菜单项
4. **快捷键支持**：为菜单项添加快捷键
5. **上下文感知**：根据文件大小、位置等调整菜单选项
