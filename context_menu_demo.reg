Windows Registry Editor Version 5.00

; BandiZip Context Menu Integration Demo
; This file shows what registry entries would be created for Windows right-click menu integration

; ========================================
; Archive Files Context Menu (ZIP files)
; ========================================

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip]
@="BandiZip"

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\Icon]
@="D:\\ai-project\\BandiZip\\build\\BandiZip.exe,0"

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\shell]

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\shell\ExtractHere]
@="智能解压到此处"

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\shell\ExtractHere\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -extract \"%1\""

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\shell\Preview]
@="预览压缩包"

[HKEY_CLASSES_ROOT\.zip\shell\BandiZip\shell\Preview\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -preview \"%1\""

; ========================================
; RAR Files Context Menu
; ========================================

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip]
@="BandiZip"

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\Icon]
@="D:\\ai-project\\BandiZip\\build\\BandiZip.exe,0"

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\shell]

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\shell\ExtractHere]
@="智能解压到此处"

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\shell\ExtractHere\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -extract \"%1\""

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\shell\Preview]
@="预览压缩包"

[HKEY_CLASSES_ROOT\.rar\shell\BandiZip\shell\Preview\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -preview \"%1\""

; ========================================
; 7Z Files Context Menu
; ========================================

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip]
@="BandiZip"

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\Icon]
@="D:\\ai-project\\BandiZip\\build\\BandiZip.exe,0"

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\shell]

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\shell\ExtractHere]
@="智能解压到此处"

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\shell\ExtractHere\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -extract \"%1\""

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\shell\Preview]
@="预览压缩包"

[HKEY_CLASSES_ROOT\.7z\shell\BandiZip\shell\Preview\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -preview \"%1\""

; ========================================
; All Files Context Menu (Compression)
; ========================================

[HKEY_CLASSES_ROOT\*\shell\BandiZip]
@="BandiZip"

[HKEY_CLASSES_ROOT\*\shell\BandiZip\Icon]
@="D:\\ai-project\\BandiZip\\build\\BandiZip.exe,0"

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell]

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\CompressZip]
@="压缩为\"%1.zip\""

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\CompressZip\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-zip \"%1\""

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\Compress7z]
@="压缩为\"%1.7z\""

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\Compress7z\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-7z \"%1\""

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\CustomCompress]
@="自定义压缩"

[HKEY_CLASSES_ROOT\*\shell\BandiZip\shell\CustomCompress\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-custom \"%1\""

; ========================================
; Directory Context Menu (Folder Compression)
; ========================================

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip]
@="BandiZip"

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\Icon]
@="D:\\ai-project\\BandiZip\\build\\BandiZip.exe,0"

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell]

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\CompressZip]
@="压缩为\"%1.zip\""

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\CompressZip\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-zip \"%1\""

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\Compress7z]
@="压缩为\"%1.7z\""

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\Compress7z\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-7z \"%1\""

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\CustomCompress]
@="自定义压缩"

[HKEY_CLASSES_ROOT\Directory\shell\BandiZip\shell\CustomCompress\command]
@="\"D:\\ai-project\\BandiZip\\build\\BandiZip.exe\" -compress-custom \"%1\""
